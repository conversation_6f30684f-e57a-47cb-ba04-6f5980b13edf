name: trago_user_app
description: Trago User App - تطبيق تراجو للمستخدمين
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.0.0"

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # UI & Design
  cupertino_icons: ^1.0.6
  google_fonts: ^6.1.0
  flutter_svg: ^2.0.9
  cached_network_image: ^3.3.0
  shimmer: ^3.0.0
  lottie: ^3.3.1
  flutter_staggered_animations: ^1.1.1

  # State Management
  provider: ^6.1.1
  riverpod: ^2.4.9
  flutter_riverpod: ^2.4.9

  # Navigation
  go_router: ^16.0.0
  auto_route: ^9.3.0+1

  # HTTP & API
  dio: ^5.4.0
  retrofit: ^4.0.3
  json_annotation: ^4.8.1
  pretty_dio_logger: ^1.3.1

  # Local Storage
  shared_preferences: ^2.2.2
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  path_provider: ^2.1.1

  # Authentication & Security
  local_auth: ^2.1.7
  crypto: ^3.0.3
  flutter_secure_storage: ^10.0.0-beta.4

  # Maps & Location
  google_maps_flutter: ^2.5.0
  location: ^8.0.1
  geocoding: ^4.0.0
  geolocator: ^14.0.2

  # Notifications
  firebase_core: ^3.15.2
  firebase_messaging: ^15.2.10
  flutter_local_notifications: ^19.3.1

  # Image & Media
  image_picker: ^1.0.4
  photo_view: ^0.15.0
  flutter_image_compress: ^2.1.0

  # Utilities
  intl: ^0.20.2
  url_launcher: ^6.2.2
  share_plus: ^11.0.0
  package_info_plus: ^8.3.0
  device_info_plus: ^11.5.0
  connectivity_plus: ^6.1.4
  permission_handler: ^12.0.1

  # Payment
  flutter_inappwebview: ^6.0.0

  # Real-time
  socket_io_client: ^3.1.2

  # Form & Validation
  flutter_form_builder: ^10.1.0
  form_builder_validators: ^11.2.0

  # Animation & Loading
  loading_animation_widget: ^1.2.0+4
  flutter_spinkit: ^5.2.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^6.0.0
  
  # Code Generation
  build_runner: ^2.4.7
  json_serializable: ^6.7.1
  retrofit_generator: ^9.2.0
  hive_generator: ^2.0.1
  auto_route_generator: ^9.0.0

flutter:
  uses-material-design: true
  generate: true

  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/
    - assets/fonts/

  fonts:
    - family: Cairo
      fonts:
        - asset: assets/fonts/Cairo-Regular.ttf
        - asset: assets/fonts/Cairo-Bold.ttf
          weight: 700
        - asset: assets/fonts/Cairo-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Cairo-Medium.ttf
          weight: 500
        - asset: assets/fonts/Cairo-Light.ttf
          weight: 300

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../features/auth/presentation/pages/login_page.dart';
import '../../features/auth/presentation/pages/register_page.dart';
import '../../features/auth/presentation/pages/otp_verification_page.dart';
import '../../features/auth/presentation/pages/forgot_password_page.dart';
import '../../features/onboarding/presentation/pages/onboarding_page.dart';
import '../../features/onboarding/presentation/pages/splash_page.dart';
import '../../features/home/<USER>/pages/home_page.dart';
import '../../features/home/<USER>/pages/main_page.dart';
import '../../features/stores/presentation/pages/stores_page.dart';
import '../../features/stores/presentation/pages/store_details_page.dart';
import '../../features/products/presentation/pages/product_details_page.dart';
import '../../features/cart/presentation/pages/cart_page.dart';
import '../../features/orders/presentation/pages/orders_page.dart';
import '../../features/orders/presentation/pages/order_details_page.dart';
import '../../features/orders/presentation/pages/order_tracking_page.dart';
import '../../features/profile/presentation/pages/profile_page.dart';
import '../../features/profile/presentation/pages/edit_profile_page.dart';
import '../../features/profile/presentation/pages/addresses_page.dart';
import '../../features/profile/presentation/pages/add_address_page.dart';
import '../../features/profile/presentation/pages/settings_page.dart';
import '../../features/search/presentation/pages/search_page.dart';
import '../../features/notifications/presentation/pages/notifications_page.dart';
import '../services/storage_service.dart';

// Route Names
class AppRoutes {
  static const String splash = '/';
  static const String onboarding = '/onboarding';
  static const String login = '/login';
  static const String register = '/register';
  static const String otpVerification = '/otp-verification';
  static const String forgotPassword = '/forgot-password';
  static const String main = '/main';
  static const String home = '/home';
  static const String stores = '/stores';
  static const String storeDetails = '/store/:storeId';
  static const String productDetails = '/product/:productId';
  static const String cart = '/cart';
  static const String orders = '/orders';
  static const String orderDetails = '/order/:orderId';
  static const String orderTracking = '/order/:orderId/tracking';
  static const String profile = '/profile';
  static const String editProfile = '/profile/edit';
  static const String addresses = '/profile/addresses';
  static const String addAddress = '/profile/addresses/add';
  static const String settings = '/profile/settings';
  static const String search = '/search';
  static const String notifications = '/notifications';
}

// Router Provider
final appRouterProvider = Provider<GoRouter>((ref) {
  return GoRouter(
    initialLocation: AppRoutes.splash,
    debugLogDiagnostics: true,
    redirect: (context, state) async {
      // Check if user is logged in
      final isLoggedIn = await StorageService.isUserLoggedIn();

      // Check if onboarding is completed
      final onboardingCompleted =
          StorageService.getBool('onboarding_completed') ?? false;

      final currentLocation = state.uri.toString();

      // If on splash page, determine where to redirect
      if (currentLocation == AppRoutes.splash) {
        if (!onboardingCompleted) {
          return AppRoutes.onboarding;
        } else if (!isLoggedIn) {
          return AppRoutes.login;
        } else {
          return AppRoutes.main;
        }
      }

      // If not logged in and trying to access protected routes
      if (!isLoggedIn && _isProtectedRoute(currentLocation)) {
        return AppRoutes.login;
      }

      // If logged in and trying to access auth routes
      if (isLoggedIn && _isAuthRoute(currentLocation)) {
        return AppRoutes.main;
      }

      return null; // No redirect needed
    },
    routes: [
      // Splash Route
      GoRoute(
        path: AppRoutes.splash,
        name: 'splash',
        builder: (context, state) => const SplashPage(),
      ),

      // Onboarding Route
      GoRoute(
        path: AppRoutes.onboarding,
        name: 'onboarding',
        builder: (context, state) => const OnboardingPage(),
      ),

      // Auth Routes
      GoRoute(
        path: AppRoutes.login,
        name: 'login',
        builder: (context, state) => const LoginPage(),
      ),

      GoRoute(
        path: AppRoutes.register,
        name: 'register',
        builder: (context, state) => const RegisterPage(),
      ),

      GoRoute(
        path: AppRoutes.otpVerification,
        name: 'otp-verification',
        builder: (context, state) {
          final phone = state.queryParameters['phone'] ?? '';
          return OtpVerificationPage(phone: phone);
        },
      ),

      GoRoute(
        path: AppRoutes.forgotPassword,
        name: 'forgot-password',
        builder: (context, state) => const ForgotPasswordPage(),
      ),

      // Main App Routes
      ShellRoute(
        builder: (context, state, child) => MainPage(child: child),
        routes: [
          GoRoute(
            path: AppRoutes.home,
            name: 'home',
            builder: (context, state) => const HomePage(),
          ),
          GoRoute(
            path: AppRoutes.stores,
            name: 'stores',
            builder: (context, state) => const StoresPage(),
          ),
          GoRoute(
            path: AppRoutes.orders,
            name: 'orders',
            builder: (context, state) => const OrdersPage(),
          ),
          GoRoute(
            path: AppRoutes.profile,
            name: 'profile',
            builder: (context, state) => const ProfilePage(),
          ),
        ],
      ),

      GoRoute(
        path: AppRoutes.main,
        name: 'main',
        redirect: (context, state) => AppRoutes.home,
      ),

      // Store Details Route
      GoRoute(
        path: AppRoutes.storeDetails,
        name: 'store-details',
        builder: (context, state) {
          final storeId = state.pathParameters['storeId']!;
          return StoreDetailsPage(storeId: storeId);
        },
      ),

      // Product Details Route
      GoRoute(
        path: AppRoutes.productDetails,
        name: 'product-details',
        builder: (context, state) {
          final productId = state.pathParameters['productId']!;
          return ProductDetailsPage(productId: productId);
        },
      ),

      // Cart Route
      GoRoute(
        path: AppRoutes.cart,
        name: 'cart',
        builder: (context, state) => const CartPage(),
      ),

      // Order Details Route
      GoRoute(
        path: AppRoutes.orderDetails,
        name: 'order-details',
        builder: (context, state) {
          final orderId = state.pathParameters['orderId']!;
          return OrderDetailsPage(orderId: orderId);
        },
      ),

      // Order Tracking Route
      GoRoute(
        path: AppRoutes.orderTracking,
        name: 'order-tracking',
        builder: (context, state) {
          final orderId = state.pathParameters['orderId']!;
          return OrderTrackingPage(orderId: orderId);
        },
      ),

      // Profile Routes
      GoRoute(
        path: AppRoutes.editProfile,
        name: 'edit-profile',
        builder: (context, state) => const EditProfilePage(),
      ),

      GoRoute(
        path: AppRoutes.addresses,
        name: 'addresses',
        builder: (context, state) => const AddressesPage(),
      ),

      GoRoute(
        path: AppRoutes.addAddress,
        name: 'add-address',
        builder: (context, state) => const AddAddressPage(),
      ),

      GoRoute(
        path: AppRoutes.settings,
        name: 'settings',
        builder: (context, state) => const SettingsPage(),
      ),

      // Search Route
      GoRoute(
        path: AppRoutes.search,
        name: 'search',
        builder: (context, state) {
          final query = state.queryParameters['q'] ?? '';
          return SearchPage(initialQuery: query);
        },
      ),

      // Notifications Route
      GoRoute(
        path: AppRoutes.notifications,
        name: 'notifications',
        builder: (context, state) => const NotificationsPage(),
      ),
    ],
    errorBuilder: (context, state) => Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              'صفحة غير موجودة',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              'المسار: ${state.uri.toString()}',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => context.go(AppRoutes.home),
              child: const Text('العودة للرئيسية'),
            ),
          ],
        ),
      ),
    ),
  );
});

// Helper functions
bool _isProtectedRoute(String location) {
  const protectedRoutes = [
    '/main',
    '/home',
    '/stores',
    '/orders',
    '/profile',
    '/cart',
    '/notifications',
  ];

  return protectedRoutes.any((route) => location.startsWith(route));
}

bool _isAuthRoute(String location) {
  const authRoutes = [
    '/login',
    '/register',
    '/otp-verification',
    '/forgot-password',
  ];

  return authRoutes.contains(location);
}
